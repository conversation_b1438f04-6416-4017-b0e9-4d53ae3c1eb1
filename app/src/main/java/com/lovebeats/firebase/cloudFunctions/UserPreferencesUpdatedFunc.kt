package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.firebase.functions.FirebaseFunctions
import com.lovebeats.analytics.MP_USER_PREFS_UPDATED
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.models.UserObject
import timber.log.Timber

object UserPreferencesUpdatedFunc {

    fun makeUserPreferencesUpdatedAPICall(context: Context?, mainUserId: String) {
        if (mainUserId.isNotEmpty() && !UserObject.userPreferencesUpdatedAPICalled) {
            Timber.d("Calling UserPreferencesUpdatedFunc")
            UserObject.userPreferencesUpdatedAPICalled = true
            callUserPreferencesUpdatedCloudFunction(context, mainUserId)
        }
    }

    private fun callUserPreferencesUpdatedCloudFunction(context: Context?, uid: String) {
        try {
            val functions = FirebaseFunctions.getInstance()
            val data = hashMapOf(
                "uid" to uid,
            )
            functions
                .getHttpsCallable("userPreferencesUpdated")
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        MixPanelAnalyticsTrackingService.logApiSuccessEvent(context, MP_USER_PREFS_UPDATED)
                    } else {
                        MixPanelAnalyticsTrackingService.logApiFailedEvent(
                            context,
                            MP_USER_PREFS_UPDATED,
                            task.exception?.message.toString()
                        )
                    }
                }
        }catch (exception: Exception) {
            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                context,
                MP_USER_PREFS_UPDATED,
                exception.message.toString()
            )
            Timber.e("Exception in callUserPreferencesUpdatedCloudFunction API: $exception")
        }
    }
}