package com.lovebeats.firebase.cloudFunctions

import android.content.Context
import com.google.firebase.functions.FirebaseFunctions
import com.google.gson.Gson
import com.lovebeats.analytics.MP_NUM_LIKE_RESTRICTION
import com.lovebeats.analytics.MixPanelAnalyticsTrackingService
import com.lovebeats.firebase.database.FirebaseDatabaseUtil
import com.lovebeats.models.TimeServer
import com.lovebeats.models.UserObject
import com.lovebeats.utils.Utils.Companion.extractDateStringFromDateObject
import com.lovebeats.utils.Utils.Companion.getDateFromTimestamp
import com.lovebeats.utils.Utils.Companion.getDeviceDate
import timber.log.Timber

object NumLikesRestriction {

    var deviceLikeCounter = 0
    var deviceDateBasedOnServer: String? = null
    var likesThreshold = 10

    var tempLikeCounterInMemory = 0

    fun setDeviceLikeCounterAndDate(context: Context, callback: () -> Unit) {
        try {
            if (UserObject.isLoveBeatPlusUser == false) {
                FirebaseFunctions.getInstance()
                        .getHttpsCallable("getUtcTimeStamp")
                        .call()
                        .addOnCompleteListener { task ->
                            if (task.isSuccessful) {
                                val result = Gson().fromJson(task.result?.getData()?.toString(), TimeServer::class.java)
                                MixPanelAnalyticsTrackingService.logApiSuccessEvent(context, MP_NUM_LIKE_RESTRICTION)
                                if (result.timeStamp != null) {
                                    Timber.d("Testing time: ${result.timeStamp}")
                                    val dateObject = getDateFromTimestamp(result.timeStamp)
                                    deviceDateBasedOnServer = extractDateStringFromDateObject(dateObject)
                                    setLikesCounter(context, callback)
                                } else {
                                    setDateFromDevice(callback)
                                }
                            }else {
                                MixPanelAnalyticsTrackingService.logApiFailedEvent(
                                    context,
                                    MP_NUM_LIKE_RESTRICTION,
                                    task.exception?.message.toString()
                                )
                                setDateFromDevice(callback)
                            }
                        }
            }else {
                callback()
            }
        } catch (exception: Exception) {
            MixPanelAnalyticsTrackingService.logApiFailedEvent(
                context,
                MP_NUM_LIKE_RESTRICTION,
                exception.message.toString()
            )
            Timber.e("Exception in getting time from time server: $exception")
            setDateFromDevice(callback)
        }
    }

    fun updateLikeCounterOnDeviceAndServer(context: Context) {
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
        deviceDateBasedOnServer?.let {
            firebaseDatabaseUtil.writeLikeCounter(it, deviceLikeCounter)
        }
    }

    private fun setDateFromDevice(callback: () -> Unit) {
        deviceDateBasedOnServer = getDeviceDate()
        callback()
    }

    private fun setLikesCounter(context: Context, callback: () -> Unit) {
        val firebaseDatabaseUtil = FirebaseDatabaseUtil(context)
        deviceDateBasedOnServer?.let { date ->
            firebaseDatabaseUtil.readLikeCounter(date) { count ->
                deviceLikeCounter = count
                callback()
            }
        }
    }
}
