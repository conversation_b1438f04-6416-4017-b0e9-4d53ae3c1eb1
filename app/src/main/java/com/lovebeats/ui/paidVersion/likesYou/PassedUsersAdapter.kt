package com.lovebeats.ui.paidVersion.likesYou

import android.app.Activity
import android.content.Intent
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.RenderEffect
import android.graphics.Shader
import android.os.Build
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.databinding.ItemLikesYouBinding
import com.lovebeats.databinding.ItemLikesYouLockedBinding
import com.lovebeats.glide.ImageLoaderModule
import com.lovebeats.models.LikesTabUser
import com.lovebeats.models.UserObject
import com.lovebeats.subscriptions.views.SubscriptionActivity
import com.lovebeats.ui.SpacesImagesViewModel
import com.lovebeats.ui.home.browseProfiles.OtherUserProfileModal
import com.lovebeats.ui.paidVersion.likesYou.LikesYouAdapter.Companion.EXTRA_PASSED_USER
import com.lovebeats.utils.AppUtils
import com.lovebeats.utils.Constants
import com.lovebeats.utils.Utils
import java.text.SimpleDateFormat
import java.util.Locale

class PassedUsersAdapter(private val context: Activity, private val spacesImagesViewModel: SpacesImagesViewModel) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        const val VIEW_TYPE_UNLOCKED = 0
        const val VIEW_TYPE_LOCKED = 1
    }

    private val users = arrayListOf<LikesTabUser>()

    override fun getItemViewType(position: Int): Int {
        return if (UserObject.isLoveBeatPlusUser == true) {
            VIEW_TYPE_UNLOCKED
        } else {
            VIEW_TYPE_LOCKED
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_UNLOCKED -> {
                val itemBinding = ItemLikesYouBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                itemBinding.userProfilePhoto.clipToOutline = true
                PassedUserViewHolder(itemBinding)
            }
            VIEW_TYPE_LOCKED -> {
                val itemBinding = ItemLikesYouLockedBinding.inflate(LayoutInflater.from(parent.context), parent, false)
                itemBinding.userProfilePhoto.clipToOutline = true
                PassedUserLockedViewHolder(itemBinding)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemCount(): Int = users.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is PassedUserViewHolder -> {
                holder.bind(users[position])
            }
            is PassedUserLockedViewHolder -> {
                holder.bind(users[position])
            }
        }
    }

    fun appendUsers(newUsers: List<LikesTabUser>) {
        val start = users.size
        users.addAll(newUsers)
        notifyItemRangeInserted(start, newUsers.size)
    }

    fun setUsers(newUsers: List<LikesTabUser>) {
        users.clear()
        users.addAll(newUsers)
        notifyDataSetChanged()
    }

    inner class PassedUserViewHolder(private val binding: ItemLikesYouBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(user: LikesTabUser) {

            binding.userName.text = user.name.toString()

            val sdf = SimpleDateFormat("MM/dd/yyyy", Locale.US)
            val date = sdf.parse(user?.dob.toString())
            binding.userHeight.text = Utils.getAge(date).toString()

            val width = AppUtils.getDpForImages(context, 156f)
            val height = AppUtils.getDpForImages(context, 156f)
            ImageLoaderModule.loadImageIntoImageViewNoCropWithLoadingAndCallback(
                context, spacesImagesViewModel,
                "${user.uid}/${Constants.photoFileName1}",
                binding.userProfilePhoto,
                shouldCacheImage = true, width = width, height = height
            )

            binding.userProfilePhoto.setOnClickListener {
                val intent = Intent(context, OtherUserProfileModal::class.java)
                intent.putExtra(EXTRA_PASSED_USER, user)
                context.startActivity(intent)
            }
        }
    }

    inner class PassedUserLockedViewHolder(private val binding: ItemLikesYouLockedBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(user: LikesTabUser) {

            val width = AppUtils.getDpForImages(context, 156f)
            val height = AppUtils.getDpForImages(context, 156f)

            // Load the image using the callback-enabled method
            ImageLoaderModule.loadImageIntoImageViewWithLoadingAndCallback(
                context, context, spacesImagesViewModel,
                "${user.uid}/${Constants.photoFileName1}",
                binding.userProfilePhoto,
                onSuccess = {
                    // Apply premium blur effect after image loads successfully
                    applyPremiumBlurEffect()
                },
                onFailure = {
                    // Apply blur effect even on failure to maintain consistency
                    applyPremiumBlurEffect()
                },
                shouldCacheImage = true
            )

            // Click handler to show subscription screen
            binding.root.setOnClickListener {
                val intent = Intent(context, SubscriptionActivity::class.java)
                intent.putExtra(SubscriptionActivity.SUBSCRIPTION_ORIGIN, SubscriptionActivity.PASSED_LOCKED)
                context.startActivity(intent)
            }
        }

        private fun applyPremiumBlurEffect() {
            // Create frosted glass effect with progressive blur
            val colorMatrix = ColorMatrix()
            colorMatrix.setSaturation(0.2f) // Keep slight color for premium feel

            // Apply sophisticated darkening for premium locked appearance
            val premiumMatrix = ColorMatrix(floatArrayOf(
                0.8f, 0.0f, 0.0f, 0.0f, -15f,  // Red channel - preserve more detail
                0.0f, 0.8f, 0.0f, 0.0f, -15f,  // Green channel - preserve more detail
                0.0f, 0.0f, 0.8f, 0.0f, -15f,  // Blue channel - preserve more detail
                0.0f, 0.0f, 0.0f, 1.0f, 0.0f   // Alpha channel - keep as is
            ))

            colorMatrix.postConcat(premiumMatrix)
            val filter = ColorMatrixColorFilter(colorMatrix)
            binding.userProfilePhoto.colorFilter = filter

            // Apply blur effect for Android 12+ (API 31+)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val blurEffect = RenderEffect.createBlurEffect(8f, 8f, Shader.TileMode.CLAMP)
                binding.userProfilePhoto.setRenderEffect(blurEffect)
            }

            // Reduce opacity for premium locked appearance
            binding.userProfilePhoto.alpha = 0.7f
        }
    }
}
