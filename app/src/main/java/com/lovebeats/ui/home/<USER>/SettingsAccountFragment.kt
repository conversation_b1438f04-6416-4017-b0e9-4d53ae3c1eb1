package com.lovebeats.ui.home.settings

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lovebeats.R
import com.lovebeats.analytics.AnalyticsTrackingService
import com.lovebeats.databinding.FragmentSettingsAccountBinding
import com.lovebeats.services.AuthService
import com.lovebeats.ui.adapter.SettingsHomeSupernovaActivityViewRecyclerViewAdapter
import com.lovebeats.ui.login.Intro1Activity
import com.lovebeats.utils.AppLogic

class SettingsAccountFragment : Fragment() {

    private lateinit var viewRecyclerView: RecyclerView
    private val accountSettingsData: ArrayList<String> = ArrayList()

    private lateinit var headerLeftBackImageView: ImageView
    private lateinit var settingsSettingsFragment: Fragment

    private var _binding: FragmentSettingsAccountBinding? = null
    private val binding get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        settingsSettingsFragment = SettingsSettingsFragment()
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSettingsAccountBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        init(view)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun init(layout: View) {

        if (accountSettingsData.isEmpty()) {
            addAccountSettingsData()
        }

        // Configure View component
        viewRecyclerView = layout.findViewById(R.id.account_view_recycler_view)
        viewRecyclerView.layoutManager = LinearLayoutManager(activity, LinearLayoutManager.VERTICAL, false)
        viewRecyclerView.adapter = SettingsHomeSupernovaActivityViewRecyclerViewAdapter(activity, accountSettingsData, { settingsItem: String -> settingsItemClicked(settingsItem) }, 3)

        headerLeftBackImageView = layout.findViewById(R.id.account_header_left_image_view)

        headerLeftBackImageView.setOnClickListener {

            fragmentManager?.beginTransaction()?.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)?.replace(R.id.main_container, settingsSettingsFragment)?.commit()
        }
    }

    private fun addAccountSettingsData() {
        accountSettingsData.add(resources.getString(R.string.settings_account_item_2))
        accountSettingsData.add(resources.getString(R.string.settings_account_item_3))
        accountSettingsData.add(resources.getString(R.string.settings_account_item_4))
    }

    private fun settingsItemClicked(settingsItem: String) {

        when (settingsItem) {
            resources.getString(R.string.settings_account_item_1) ->
                updatePhoneNumber()
            resources.getString(R.string.settings_account_item_2) ->
                updateEmail()
            resources.getString(R.string.settings_account_item_3) ->
                signOut()
            resources.getString(R.string.settings_account_item_4) ->
                AppLogic.deleteAccount(activity, binding.progressBar)
        }
    }

    private fun openBrowser(url: String) {
        val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        activity?.startActivity(browserIntent)
    }

    private fun signOut() {

        AuthService.cleanup(activity)

        context?.let {
            startActivity(Intro1Activity.newIntentWithClearBackStack(it))
        }

        AnalyticsTrackingService.resetUserProperties(context)
    }

    private fun updatePhoneNumber() {
        val intent = Intent(context, UpdatePhoneNumberActivity::class.java)
        startActivity(intent)
    }

    private fun updateEmail() {
        val intent = Intent(context, UpdateEmailActivity::class.java)
        startActivity(intent)
    }
}
